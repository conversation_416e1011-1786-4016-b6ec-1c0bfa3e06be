// Utility functions for formatting numbers, currency, and percentages

export const formatNumber = (value: number | undefined | null): string => {
  if (value === undefined || value === null || isNaN(value)) return '0';
  return value.toLocaleString();
};

export const formatNumberToZero = (
  value: number | undefined | null,
): string => {
  if (value === undefined || value === null || isNaN(value)) return '0';
  return value.toFixed(0);
};

export const formatCurrency = (value: number | undefined | null): string => {
  if (value === undefined || value === null || isNaN(value)) return '0';
  return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

export const formatCurrencyForTurnCost = (
  value: number | undefined | null,
): string => {
  if (value === undefined || value === null || isNaN(value)) return '0';
  return `$${value.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
};

export const formatPercentage = (
  value: number | undefined | null,
  decimals: number = 1,
): string => {
  if (value === undefined || value === null || isNaN(value) || !isFinite(value))
    return '0';
  return `${value.toFixed(decimals)}%`;
};

export const formatPeriod = (startDate: string, endDate: string): string => {
  if (!startDate || !endDate) return '';
  const start = new Date(startDate);
  const end = new Date(endDate);
  return `(${start.getMonth() + 1}/${start.getDate()}/${start.getFullYear()}-${end.getMonth() + 1}/${end.getDate()}/${end.getFullYear()})`;
};

export const formatDateToMMDDYYYY = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const year = date.getFullYear();

  return `${month}/${day}/${year}`;
};

export const formatFinancialDateRange = (
  startMonth: number | undefined,
  startDay: number | undefined,
  startYear: number | undefined,
  endMonth: number | undefined,
  endDay: number | undefined,
  endYear: number | undefined,
): string => {
  if (
    !startMonth ||
    !startDay ||
    !startYear ||
    !endMonth ||
    !endDay ||
    !endYear
  ) {
    return '';
  }

  // Format as mm/dd/yyyy-mm/dd/yyyy
  const startFormatted = `${startMonth.toString().padStart(2, '0')}/${startDay.toString().padStart(2, '0')}/${startYear}`;
  const endFormatted = `${endMonth.toString().padStart(2, '0')}/${endDay.toString().padStart(2, '0')}/${endYear}`;

  return `(${startFormatted}-${endFormatted})`;
};
