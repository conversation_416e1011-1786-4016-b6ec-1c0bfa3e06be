import { useCallback } from 'react';
import {
  getGoogleReviews,
  getImages,
  getImagesBase64,
  getJTurnerScore,
  getPropertyStrategy,
  getScoreCard,
  getScoreCardFilterOptions,
  getScoreCardFinancial,
  getScoreCardOccupancy,
  getScoreCardOperations,
  getScoreCardRent,
  getSubmarketOccupancy,
} from '@/api/scorecardApi';
import {
  setError,
  setFinancialDateRange,
  setFinancialMetrics,
  setLoading,
  setOccupancyMetrics,
  setPerformanceRows,
  setPropertyInfo,
  setRawApiData,
  setRentalMetrics,
  setReputationMetrics,
  setSectionError,
  setSectionLoading,
  setSummaryTexts,
  setYtdTurnCost,
  type ErrorStates,
  type LoadingStates,
  type PropertyInfo as PropertyInfoType,
} from '@/slice/scoreCardSlice';
import { RootState } from '@/store';
import dayjs from 'dayjs';
import { useDispatch, useSelector } from 'react-redux';
import {
  ScoreCardFinancialResponse,
  ScoreCardOccupancyResponse,
  ScoreCardOperationsResponse,
  ScoreCardRentResponse,
} from '@/types/scorecardTypes';
import {
  FilterOptionsResponse,
  GoogleReviewsResponse,
  ImagesResponse,
  JTurnerScoreResponse,
  PropertyStrategyResponse,
  ScorecardAPIParams,
  ScoreCardResponse,
} from '../types/scorecardTypes';
import {
  generateSummaryText,
  getFinancialDateRange,
  mergeScoreCardResponses,
  transformFinancialMetrics,
  transformOccupancyMetrics,
  transformPerformanceRows,
  transformPropertyInfo,
  transformRentalMetrics,
  transformReputationMetrics,
  updatePropertyInfoWithScorecard,
} from '../utils/dataTransformers';
import { formatCurrencyForTurnCost } from '../utils/formatUtils';

export const useScorecardData = () => {
  const dispatch = useDispatch();
  const { filters } = useSelector((state: RootState) => state.scorecard);

  const setSectionLoadingStates = useCallback(
    (loading: boolean) => {
      const sections: (keyof LoadingStates)[] = [
        'propertyInfo',
        'occupancyMetrics',
        'rentalMetrics',
        'financialMetrics',
        'performanceRows',
        'reputationMetrics',
        'summaryTexts',
      ];
      sections.forEach((section) => {
        dispatch(setSectionLoading({ section, loading }));
      });
    },
    [dispatch],
  );

  const clearSectionErrors = useCallback(() => {
    const sections: (keyof ErrorStates)[] = [
      'propertyInfo',
      'occupancyMetrics',
      'rentalMetrics',
      'financialMetrics',
      'performanceRows',
      'reputationMetrics',
      'summaryTexts',
    ];
    sections.forEach((section) => {
      dispatch(setSectionError({ section, error: null }));
    });
  }, [dispatch]);

  const fetchPropertyInfo = useCallback(
    async (
      propertyCode: string,
      startDate: string,
      endDate: string,
      filterOptionsResponse?: FilterOptionsResponse,
    ) => {
      try {
        const [propertyStrategyResponse, imagesResponse] = await Promise.all([
          getPropertyStrategy({ propertyCode, startDate, endDate }),
          getImagesBase64({ bu: parseInt(propertyCode) }).catch(
            async (error) => {
              console.warn(
                'Base64 image fetch failed, falling back to regular images:',
                error,
              );
              return getImages({ bu: parseInt(propertyCode) });
            },
          ),
        ]);

        if (filterOptionsResponse) {
          const propertyStrategy =
            propertyStrategyResponse?.data?.[0]?.Property_Strategy_This_Year ||
            '0';
          const sameStore =
            propertyStrategyResponse?.data?.[0]?.Same_Store || '0';

          // Use base64 image if available, fallback to URL
          const imageData = imagesResponse?.data?.[0];
          const imageUrl = imageData?.image_base64 || imageData?.image_url;

          const propertyInfo = transformPropertyInfo(
            filterOptionsResponse,
            propertyCode,
            propertyStrategy,
            sameStore,
            imageUrl,
          );

          dispatch(setPropertyInfo(propertyInfo));
          dispatch(
            setSectionLoading({ section: 'propertyInfo', loading: false }),
          );

          return {
            propertyInfo,
            propertyStrategyResponse,
            imagesResponse,
          };
        } else {
          console.warn(
            'No filter options available for property info transformation',
          );
          dispatch(
            setSectionLoading({ section: 'propertyInfo', loading: false }),
          );
        }
      } catch (error) {
        console.error('Error fetching property info:', error);
        dispatch(
          setSectionError({
            section: 'propertyInfo',
            error: 'Failed to load property info',
          }),
        );
        dispatch(
          setSectionLoading({ section: 'propertyInfo', loading: false }),
        );
      }
      return null;
    },
    [dispatch],
  );

  const fetchScorecardMetrics = useCallback(
    async (params: ScorecardAPIParams, propertyUnits?: number) => {
      try {
        const scorecardResponse = await getScoreCard(params);

        const occupancyMetrics = transformOccupancyMetrics(
          scorecardResponse,
          undefined,
          propertyUnits,
        );
        const rentalMetrics = transformRentalMetrics(scorecardResponse);
        const financialMetrics = transformFinancialMetrics(scorecardResponse);
        const performanceRows = transformPerformanceRows(
          scorecardResponse,
          propertyUnits,
          filters.endDate,
        );
        const financialDateRange = getFinancialDateRange(scorecardResponse);

        dispatch(setOccupancyMetrics(occupancyMetrics));
        dispatch(setRentalMetrics(rentalMetrics));
        dispatch(setFinancialMetrics(financialMetrics));
        dispatch(setPerformanceRows(performanceRows));
        dispatch(setFinancialDateRange(financialDateRange));
        dispatch(setRawApiData(scorecardResponse?.data || null));
        dispatch(
          setYtdTurnCost(
            formatCurrencyForTurnCost(scorecardResponse?.data?.COST_Per_Turn),
          ),
        );

        (
          [
            'occupancyMetrics',
            'rentalMetrics',
            'financialMetrics',
            'performanceRows',
          ] as const
        ).forEach((section) => {
          dispatch(setSectionLoading({ section, loading: false }));
        });

        return scorecardResponse;
      } catch (error) {
        console.error('Error fetching scorecard metrics:', error);
        (
          [
            'occupancyMetrics',
            'rentalMetrics',
            'financialMetrics',
            'performanceRows',
          ] as const
        ).forEach((section) => {
          dispatch(
            setSectionError({
              section,
              error: `Failed to load ${section}`,
            }),
          );
          dispatch(setSectionLoading({ section, loading: false }));
        });
      }
      return null;
    },
    [dispatch],
  );

  const fetchScorecardMetricsParallel = useCallback(
    async (params: ScorecardAPIParams, propertyUnits?: number) => {
      try {
        // Set loading states for all sections
        (
          [
            'occupancyMetrics',
            'rentalMetrics',
            'financialMetrics',
            'performanceRows',
          ] as const
        ).forEach((section) => {
          dispatch(setSectionLoading({ section, loading: true }));
          dispatch(setSectionError({ section, error: null }));
        });

        // Store API responses for final merging
        let occupancyData: ScoreCardOccupancyResponse | null = null;
        let rentData: ScoreCardRentResponse | null = null;
        let financialData: ScoreCardFinancialResponse | null = null;
        let operationsData: ScoreCardOperationsResponse | null = null;

        // Progressive loading: Handle each API independently as it completes
        const occupancyPromise = getScoreCardOccupancy(params)
          .then((response) => {
            occupancyData = response;

            // Create partial merged data for transformation
            const partialMergedData = mergeScoreCardResponses(
              response,
              null,
              null,
              null,
            );
            const partialScorecard = { data: partialMergedData };

            const occupancyMetrics = transformOccupancyMetrics(
              partialScorecard,
              undefined,
              propertyUnits,
            );
            dispatch(setOccupancyMetrics(occupancyMetrics));
            dispatch(
              setSectionLoading({
                section: 'occupancyMetrics',
                loading: false,
              }),
            );

            console.log('Occupancy data loaded successfully');
            return response;
          })
          .catch((error) => {
            console.error('Error fetching occupancy data:', error);
            dispatch(
              setSectionError({
                section: 'occupancyMetrics',
                error: 'Failed to load occupancy data',
              }),
            );
            dispatch(
              setSectionLoading({
                section: 'occupancyMetrics',
                loading: false,
              }),
            );
            return null;
          });

        const rentPromise = getScoreCardRent(params)
          .then((response) => {
            rentData = response;

            // Create partial merged data for transformation
            const partialMergedData = mergeScoreCardResponses(
              null,
              response,
              null,
              null,
            );
            const partialScorecard = { data: partialMergedData };

            const rentalMetrics = transformRentalMetrics(partialScorecard);
            dispatch(setRentalMetrics(rentalMetrics));
            dispatch(
              setSectionLoading({ section: 'rentalMetrics', loading: false }),
            );

            console.log('Rent data loaded successfully');
            return response;
          })
          .catch((error) => {
            console.error('Error fetching rent data:', error);
            dispatch(
              setSectionError({
                section: 'rentalMetrics',
                error: 'Failed to load rental data',
              }),
            );
            dispatch(
              setSectionLoading({ section: 'rentalMetrics', loading: false }),
            );
            return null;
          });

        const financialPromise = getScoreCardFinancial(params)
          .then((response) => {
            financialData = response;

            // Create partial merged data for transformation
            const partialMergedData = mergeScoreCardResponses(
              null,
              null,
              response,
              null,
            );
            const partialScorecard = { data: partialMergedData };

            const financialMetrics =
              transformFinancialMetrics(partialScorecard);
            dispatch(setFinancialMetrics(financialMetrics));
            dispatch(
              setYtdTurnCost(
                formatCurrencyForTurnCost(partialMergedData.COST_Per_Turn),
              ),
            );
            dispatch(
              setSectionLoading({
                section: 'financialMetrics',
                loading: false,
              }),
            );

            console.log('Financial data loaded successfully');
            return response;
          })
          .catch((error) => {
            console.error('Error fetching financial data:', error);
            dispatch(
              setSectionError({
                section: 'financialMetrics',
                error: 'Failed to load financial data',
              }),
            );
            dispatch(
              setSectionLoading({
                section: 'financialMetrics',
                loading: false,
              }),
            );
            return null;
          });

        const operationsPromise = getScoreCardOperations(params)
          .then((response) => {
            operationsData = response;

            // Create partial merged data for transformation
            const partialMergedData = mergeScoreCardResponses(
              null,
              null,
              null,
              response,
            );
            const partialScorecard = { data: partialMergedData };

            const performanceRows = transformPerformanceRows(
              partialScorecard,
              propertyUnits,
              filters.endDate,
            );
            dispatch(setPerformanceRows(performanceRows));
            dispatch(
              setSectionLoading({ section: 'performanceRows', loading: false }),
            );

            // Set additional data from operations API (contains date range info)
            const financialDateRange = getFinancialDateRange(partialScorecard);
            dispatch(setFinancialDateRange(financialDateRange));

            console.log('Operations data loaded successfully');
            return response;
          })
          .catch((error) => {
            console.error('Error fetching operations data:', error);
            dispatch(
              setSectionError({
                section: 'performanceRows',
                error: 'Failed to load operations data',
              }),
            );
            dispatch(
              setSectionLoading({ section: 'performanceRows', loading: false }),
            );
            return null;
          });

        // Wait for all APIs to complete (but UI already updated progressively)
        const results = await Promise.allSettled([
          occupancyPromise,
          rentPromise,
          financialPromise,
          operationsPromise,
        ]);

        // Log completion status for debugging
        const completionStatus = {
          occupancy: results[0].status,
          rent: results[1].status,
          financial: results[2].status,
          operations: results[3].status,
        };
        console.log('Scorecard API completion status:', completionStatus);

        // Create final merged data for raw API data storage
        const finalMergedData = mergeScoreCardResponses(
          occupancyData,
          rentData,
          financialData,
          operationsData,
        );

        // Set final raw API data
        dispatch(setRawApiData(finalMergedData));

        // Count successful vs failed APIs for user feedback
        const successCount = Object.values(completionStatus).filter(
          (status) => status === 'fulfilled',
        ).length;
        const totalCount = Object.keys(completionStatus).length;

        if (successCount === 0) {
          throw new Error('All scorecard APIs failed to load');
        } else if (successCount < totalCount) {
          console.warn(
            `Scorecard loaded with partial data: ${successCount}/${totalCount} APIs succeeded`,
          );
        } else {
          console.log('All scorecard APIs loaded successfully');
        }

        return { data: finalMergedData };
      } catch (error) {
        console.error('Error in fetchScorecardMetricsParallel:', error);
        // Set error for all sections if there's a general error
        (
          [
            'occupancyMetrics',
            'rentalMetrics',
            'financialMetrics',
            'performanceRows',
          ] as const
        ).forEach((section) => {
          dispatch(
            setSectionError({
              section,
              error: `Failed to load ${section}`,
            }),
          );
          dispatch(setSectionLoading({ section, loading: false }));
        });
      }
      return null;
    },
    [dispatch],
  );

  const generateSummary = useCallback(
    (
      propertyInfo: PropertyInfoType | null,
      scorecardResponse: ScoreCardResponse,
      submarketOccupancy?: number,
    ) => {
      try {
        const summaryTexts = generateSummaryText(
          propertyInfo?.propertyName || 'N/A',
          scorecardResponse?.data?.Occupancy_Trend || 0,
          submarketOccupancy || 0,
          scorecardResponse?.data?.gain_loss || 0,
          scorecardResponse?.data?.t30_show || 0,
          (scorecardResponse?.data?.Occupancy_Trendt30 || 0) -
            (scorecardResponse?.data?.Occupancy_Trend || 0),
          propertyInfo?.units,
        );

        dispatch(setSummaryTexts(summaryTexts));
        dispatch(
          setSectionLoading({ section: 'summaryTexts', loading: false }),
        );
      } catch (error) {
        console.error('Error generating summary:', error);
        dispatch(
          setSectionError({
            section: 'summaryTexts',
            error: 'Failed to generate summary',
          }),
        );
        dispatch(
          setSectionLoading({ section: 'summaryTexts', loading: false }),
        );
      }
    },
    [dispatch],
  );

  const fetchSubmarketOccupancy = useCallback(
    async (
      params: ScorecardAPIParams,
      propertyInfo: PropertyInfoType,
      scorecardResponse: ScoreCardResponse,
    ) => {
      if (!propertyInfo?.submarket || propertyInfo.submarket === '0') {
        generateSummary(propertyInfo, scorecardResponse, 0);
        return;
      }

      try {
        const submarketName = propertyInfo.submarket.split('-')[0].trim();
        const submarketResponse = await getSubmarketOccupancy({
          startDate: params.startDate,
          endDate: params.endDate,
          subMarketName: submarketName,
        });
        const submarketOccupancy =
          submarketResponse?.data?.[0]?.submarket_occupancy;

        const updatedOccupancyMetrics = transformOccupancyMetrics(
          scorecardResponse,
          submarketOccupancy,
          propertyInfo.units,
        );
        dispatch(setOccupancyMetrics(updatedOccupancyMetrics));

        generateSummary(propertyInfo, scorecardResponse, submarketOccupancy);
      } catch (error) {
        console.error('Error fetching submarket occupancy:', error);
        generateSummary(propertyInfo, scorecardResponse, 0);
      }
    },
    [dispatch, generateSummary],
  );

  const fetchReputationMetrics = useCallback(
    async (propertyCode: string) => {
      try {
        const [jTurnerResponse, googleReviewsResponse] = await Promise.all([
          getJTurnerScore({ propertyCode: parseInt(propertyCode) }),
          getGoogleReviews({ propertyCode: parseInt(propertyCode) }),
        ]);

        const reputationMetrics = transformReputationMetrics(
          jTurnerResponse as JTurnerScoreResponse,
          googleReviewsResponse as GoogleReviewsResponse,
        );

        dispatch(setReputationMetrics(reputationMetrics));
        dispatch(
          setSectionLoading({ section: 'reputationMetrics', loading: false }),
        );
      } catch (error) {
        console.error('Error fetching reputation metrics:', error);
        dispatch(
          setSectionError({
            section: 'reputationMetrics',
            error: 'Failed to load reputation metrics',
          }),
        );
        dispatch(
          setSectionLoading({ section: 'reputationMetrics', loading: false }),
        );
      }
    },
    [dispatch],
  );

  const fetchScorecardData = useCallback(
    async (
      params: ScorecardAPIParams,
      filterOptions?: FilterOptionsResponse,
    ) => {
      dispatch(setLoading(true));
      dispatch(setError(null));
      setSectionLoadingStates(true);
      clearSectionErrors();

      try {
        const currentFilterOptions = filterOptions;
        const [propertyInfoResult, scorecardResponse] =
          await Promise.allSettled([
            fetchPropertyInfo(
              params.propertyCode,
              params.startDate,
              params.endDate,
              currentFilterOptions,
            ),
            fetchScorecardMetrics(params),
            fetchReputationMetrics(params.propertyCode),
          ]);

        const propertyInfo =
          propertyInfoResult.status === 'fulfilled'
            ? propertyInfoResult.value?.propertyInfo
            : null;

        const scorecard =
          scorecardResponse.status === 'fulfilled'
            ? scorecardResponse.value
            : null;

        if (propertyInfo && scorecard) {
          const updatedPropertyInfo = updatePropertyInfoWithScorecard(
            propertyInfo,
            scorecard,
          );
          dispatch(setPropertyInfo(updatedPropertyInfo));

          const occupancyMetrics = transformOccupancyMetrics(
            scorecard,
            undefined,
            propertyInfo.units,
          );
          dispatch(setOccupancyMetrics(occupancyMetrics));

          const performanceRows = transformPerformanceRows(
            scorecard,
            propertyInfo.units,
            filters.endDate,
          );
          dispatch(setPerformanceRows(performanceRows));

          await fetchSubmarketOccupancy(params, propertyInfo, scorecard);
        }
      } catch (error) {
        console.error('Error in fetchScorecardData:', error);
        dispatch(setError('Failed to load scorecard data'));
      } finally {
        dispatch(setLoading(false));
      }
    },
    [
      dispatch,
      setSectionLoadingStates,
      clearSectionErrors,
      fetchPropertyInfo,
      fetchScorecardMetrics,
      fetchReputationMetrics,
      fetchSubmarketOccupancy,
    ],
  );

  const fetchScorecardDataParallel = useCallback(
    async (
      params: ScorecardAPIParams,
      onFilterOptionsLoaded?: (filterOptions: FilterOptionsResponse) => void,
    ) => {
      dispatch(setLoading(true));
      dispatch(setError(null));
      setSectionLoadingStates(true);
      clearSectionErrors();

      try {
        const filterOptionsPromise = getScoreCardFilterOptions();
        const reputationPromise = Promise.all([
          getJTurnerScore({ propertyCode: parseInt(params.propertyCode) }),
          getGoogleReviews({ propertyCode: parseInt(params.propertyCode) }),
        ]);
        const propertyInfoPromises = Promise.all([
          getPropertyStrategy({
            propertyCode: params.propertyCode,
            startDate: params.startDate,
            endDate: params.endDate,
          }),
          getImagesBase64({ bu: parseInt(params.propertyCode) }).catch(
            async (error) => {
              console.warn(
                'Base64 image fetch failed, falling back to regular images:',
                error,
              );
              return getImages({ bu: parseInt(params.propertyCode) });
            },
          ),
        ]);

        let filterOptions: FilterOptionsResponse | null = null;
        let propertyInfoData:
          | [PropertyStrategyResponse, ImagesResponse]
          | null = null;
        let transformedPropertyInfo: PropertyInfoType | null = null;

        const generateSummaryAndDependentOps = async (
          propertyInfo: PropertyInfoType,
          scorecard: ScoreCardResponse,
        ) => {
          const updatedPropertyInfo = updatePropertyInfoWithScorecard(
            propertyInfo,
            scorecard,
          );
          dispatch(setPropertyInfo(updatedPropertyInfo));

          if (
            propertyInfo.submarket &&
            propertyInfo.submarket !== '0' &&
            propertyInfo.submarket !== null &&
            propertyInfo.submarket !== undefined
          ) {
            try {
              const submarketName = propertyInfo.submarket.split('-')[0].trim();
              const submarketResponse = await getSubmarketOccupancy({
                startDate: params.startDate,
                endDate: params.endDate,
                subMarketName: submarketName,
              });
              const submarketOccupancy =
                submarketResponse?.data?.[0]?.submarket_occupancy;

              const updatedOccupancyMetrics = transformOccupancyMetrics(
                scorecard,
                submarketOccupancy,
                propertyInfo.units,
              );
              dispatch(setOccupancyMetrics(updatedOccupancyMetrics));

              const performanceRows = transformPerformanceRows(
                scorecard,
                propertyInfo.units,
                filters.endDate,
              );
              dispatch(setPerformanceRows(performanceRows));

              generateSummary(propertyInfo, scorecard, submarketOccupancy);
            } catch (error) {
              console.error('Error fetching submarket occupancy:', error);

              const performanceRows = transformPerformanceRows(
                scorecard,
                propertyInfo.units,
                filters.endDate,
              );
              dispatch(setPerformanceRows(performanceRows));

              generateSummary(propertyInfo, scorecard, 0);
            }
          } else {
            const performanceRows = transformPerformanceRows(
              scorecard,
              propertyInfo.units,
              filters.endDate,
            );
            dispatch(setPerformanceRows(performanceRows));

            generateSummary(propertyInfo, scorecard, 0);
          }
        };

        filterOptionsPromise
          .then((response) => {
            filterOptions = response;
            if (onFilterOptionsLoaded) {
              onFilterOptionsLoaded(response);
            }

            if (propertyInfoData) {
              const propertyStrategy =
                propertyInfoData[0]?.data?.[0]?.Property_Strategy_This_Year ||
                '0';
              const sameStore =
                propertyInfoData[0]?.data?.[0]?.Same_Store || '0';
              const imageData = propertyInfoData[1]?.data?.[0];
              const imageUrl = imageData?.image_base64 || imageData?.image_url;

              transformedPropertyInfo = transformPropertyInfo(
                response,
                params.propertyCode,
                propertyStrategy,
                sameStore,
                imageUrl,
              );

              dispatch(setPropertyInfo(transformedPropertyInfo));
              dispatch(
                setSectionLoading({ section: 'propertyInfo', loading: false }),
              );
            }
          })
          .catch((error) => {
            console.error('Error loading filter options:', error);
          });

        // Call the new parallel scorecard metrics function
        let scorecardData: ScoreCardResponse | null = null;
        const scorecardPromise = fetchScorecardMetricsParallel(params).then(
          (response) => {
            scorecardData = response;
            return response;
          },
        );

        reputationPromise
          .then(([jTurnerResponse, googleReviewsResponse]) => {
            const reputationMetrics = transformReputationMetrics(
              jTurnerResponse as JTurnerScoreResponse,
              googleReviewsResponse as GoogleReviewsResponse,
            );

            dispatch(setReputationMetrics(reputationMetrics));
            dispatch(
              setSectionLoading({
                section: 'reputationMetrics',
                loading: false,
              }),
            );
          })
          .catch((error) => {
            console.error('Error fetching reputation metrics:', error);
            dispatch(
              setSectionError({
                section: 'reputationMetrics',
                error: 'Failed to load reputation metrics',
              }),
            );
            dispatch(
              setSectionLoading({
                section: 'reputationMetrics',
                loading: false,
              }),
            );
          });

        propertyInfoPromises
          .then((responses) => {
            propertyInfoData = responses;

            if (filterOptions) {
              const propertyStrategy =
                responses[0]?.data?.[0]?.Property_Strategy_This_Year || '0';
              const sameStore = responses[0]?.data?.[0]?.Same_Store || '0';
              const imageData = responses[1]?.data?.[0];
              const imageUrl = imageData?.image_base64 || imageData?.image_url;

              transformedPropertyInfo = transformPropertyInfo(
                filterOptions,
                params.propertyCode,
                propertyStrategy,
                sameStore,
                imageUrl,
              );

              dispatch(setPropertyInfo(transformedPropertyInfo));
              dispatch(
                setSectionLoading({ section: 'propertyInfo', loading: false }),
              );
            }
          })
          .catch((error) => {
            console.error('Error fetching property info:', error);
            dispatch(
              setSectionError({
                section: 'propertyInfo',
                error: 'Failed to load property info',
              }),
            );
            dispatch(
              setSectionLoading({ section: 'propertyInfo', loading: false }),
            );
          });

        await Promise.allSettled([
          filterOptionsPromise,
          scorecardPromise,
          reputationPromise,
          propertyInfoPromises,
        ]);

        // Generate summary after all promises have settled
        if (transformedPropertyInfo && scorecardData) {
          try {
            await generateSummaryAndDependentOps(
              transformedPropertyInfo,
              scorecardData,
            );
          } catch (error) {
            console.error('Error generating summary:', error);
            dispatch(
              setSectionError({
                section: 'summaryTexts',
                error: 'Failed to generate summary',
              }),
            );
            dispatch(
              setSectionLoading({ section: 'summaryTexts', loading: false }),
            );
          }
        } else {
          // Fallback: set summary loading to false if we couldn't generate summary
          console.warn('Unable to generate summary: missing required data', {
            hasPropertyInfo: !!transformedPropertyInfo,
            hasScorecardData: !!scorecardData,
          });
          dispatch(
            setSectionLoading({ section: 'summaryTexts', loading: false }),
          );
        }
      } catch (error) {
        console.error('Error in fetchScorecardDataParallel:', error);
        dispatch(setError('Failed to load scorecard data'));
      } finally {
        dispatch(setLoading(false));
      }
    },
    [
      dispatch,
      setSectionLoadingStates,
      clearSectionErrors,
      generateSummary,
      fetchScorecardMetricsParallel,
    ],
  );

  const loadInitialData = useCallback(
    async (filterOptionsResponse?: FilterOptionsResponse) => {
      const endDate = dayjs().subtract(1, 'day');
      const startDate = dayjs().subtract(8, 'day');

      const defaultParams: ScorecardAPIParams = {
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        propertyCode: '22514',
      };

      if (filterOptionsResponse) {
        await fetchScorecardData(defaultParams, filterOptionsResponse);
      } else {
        try {
          const filterOptions = await getScoreCardFilterOptions();
          await fetchScorecardData(defaultParams, filterOptions);
        } catch (error) {
          console.error('Error loading initial data:', error);
          await fetchScorecardData(defaultParams);
        }
      }
    },
    [fetchScorecardData],
  );

  const loadInitialDataParallel = useCallback(
    async (
      onFilterOptionsLoaded?: (filterOptions: FilterOptionsResponse) => void,
    ) => {
      const endDate = dayjs().subtract(1, 'day');
      const startDate = dayjs().subtract(8, 'day');

      const defaultParams: ScorecardAPIParams = {
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        propertyCode: '22514',
      };

      await fetchScorecardDataParallel(defaultParams, onFilterOptionsLoaded);
    },
    [fetchScorecardDataParallel],
  );

  return {
    fetchScorecardData,
    fetchScorecardDataParallel,
    loadInitialData,
    loadInitialDataParallel,
  };
};
