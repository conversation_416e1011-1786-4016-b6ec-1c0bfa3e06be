import { UnitWalkKPIRegionTypes } from '@/api/unitWalkApis/unitWalkTypes';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelUnitWalkRegion } from '../utils/exportDownloadFormattersUnitWalk';
import { sumUnitWalkRegionColumns } from '../utils/unitWalkHelpers';

interface UnitWalkRegionTableProps {
  regionData: UnitWalkKPIRegionTypes[];
  datePeriod: string;
  filters: ReportFilters;
}

const columnWidth = 'w-[140px]';

export default function UnitWalkRegionTable(props: UnitWalkRegionTableProps) {
  const { regionData, datePeriod, filters } = props;

  const columns = ['beginning', 'additions', 'losses', 'current'] as const;
  const totals = sumUnitWalkRegionColumns(regionData, [...columns]);

  const handleExport = () => {
    downloadExcelUnitWalkRegion(regionData, filters);
  };

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={handleExport}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableSubHeaderRow className="border-t-2 border-black">
            <CommonTableHeadingCell
              className={`${columnWidth} text-start`}
              borderLeft
            >
              Region
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${columnWidth}`}>
              Beginning
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${columnWidth}`}>
              Additions
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${columnWidth}`}>
              Losses
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${columnWidth}`} borderRight>
              Current
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>
        <tbody>
          {regionData?.map((region) => {
            return (
              <CommonTableBodyRow key={region.Region}>
                <CommonTableBodyCell textAlign="text-start" borderLeft>
                  {region?.Region}
                </CommonTableBodyCell>

                <CommonTableBodyCell>
                  {region?.beginning?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {region?.additions?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {region?.losses?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {region?.current?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
          <CommonTableBodyRowTotal
            lastRowBorder={false}
            className="border-b-2 border-black"
          >
            <CommonTableBodyTotalCell textAlign="text-start" borderLeft>
              Total
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {totals.beginning?.toLocaleString('en-US',{maximumFractionDigits:0})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {totals.additions?.toLocaleString('en-US',{maximumFractionDigits:0})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {totals.losses?.toLocaleString('en-US',{maximumFractionDigits:0})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell borderRight>
              {totals.current?.toLocaleString('en-US',{maximumFractionDigits:0})}
            </CommonTableBodyTotalCell>
          </CommonTableBodyRowTotal>
        </tbody>
      </CommonTable>
    </>
  );
}
